import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/design_system_extensions.dart';
import 'package:dasso_reader/enums/text_selection_mode.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/performance/widget_optimizer.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:flutter/material.dart';

class ReadingWidget extends OptimizedStatefulWidget {
  const ReadingWidget({
    super.key,
    required this.epubPlayerKey,
    this.backgroundColor,
    this.textColor,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  OptimizedState<ReadingWidget> createState() => _ReadingWidgetState();
}

class _ReadingWidgetState extends OptimizedState<ReadingWidget> {
  BookStyle bookStyle = Prefs().bookStyle;

  @override
  Widget buildOptimized(BuildContext context) {
    // Get device-specific padding adjustments for better cross-device compatibility
    final adaptivePadding =
        DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
      context,
      ReadingDesign.controlsContainerPadding,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  constraints.maxHeight > 0 ? constraints.maxHeight : 200,
            ),
            child: Padding(
              padding: adaptivePadding,
              child: _buildReadingOptions(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReadingOptions() {
    final txtColor = Theme.of(context).colorScheme.onSurface;

    return Column(
      children: [
        // Add consistent top padding for spacing
        DesignSystem.verticalSpaceL,
        // Page Turning Method Dropdown - Clean minimal style
        Padding(
          padding: EdgeInsets.symmetric(vertical: DesignSystem.spaceM),
          child: Row(
            children: [
              // Text label on the left
              Text(
                L10n.of(context).reading_page_page_turning_method,
                style: TextStyle(
                  color: txtColor,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              // Simple dropdown with no border
              DropdownButton<PageTurn>(
                value: Prefs().pageTurnStyle,
                icon: Icon(Icons.arrow_drop_down, color: txtColor),
                underline: Container(), // Remove the underline
                dropdownColor: widget.backgroundColor ??
                    Theme.of(context).colorScheme.surface,
                style: TextStyle(color: txtColor, fontSize: 16),
                onChanged: (PageTurn? value) {
                  if (value != null) {
                    Prefs().pageTurnStyle = value;
                    widget.epubPlayerKey.currentState!
                        .changePageTurnStyle(value);
                  }
                },
                items: PageTurn.values
                    .map<DropdownMenuItem<PageTurn>>((PageTurn value) {
                  return DropdownMenuItem<PageTurn>(
                    value: value,
                    child: Text(value.getLabel(context)),
                  );
                }).toList(),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // Text Selection Mode Toggle with responsive layout
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM,
            vertical: DesignSystem.spaceS,
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Use responsive layout for smaller screens
              final isCompact = constraints.maxWidth < 300;

              if (isCompact) {
                // Vertical layout for very small screens
                return Column(
                  children: [
                    _buildTextSelectionIcon(txtColor),
                    SizedBox(height: DesignSystem.spaceS),
                    _buildTextSelectionSwitch(txtColor),
                  ],
                );
              } else {
                // Horizontal layout for normal screens
                return Row(
                  children: [
                    _buildTextSelectionIcon(txtColor),
                    SizedBox(width: DesignSystem.spaceM),
                    Expanded(child: _buildTextSelectionSwitch(txtColor)),
                  ],
                );
              }
            },
          ),
        ),

        // Add adaptive bottom padding for consistent spacing
        SizedBox(
          height: DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(
            context,
            DesignSystem.spaceL,
          ).clamp(8.0, 24.0),
        ),
      ],
    );
  }

  /// Build the text selection mode icon and label
  Widget _buildTextSelectionIcon(Color txtColor) {
    return SizedBox(
      width: 48.0,
      height: 60.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Prefs().textSelectionMode == TextSelectionMode.segmentation
                ? Icons.auto_fix_high_outlined
                : Icons.text_fields_outlined,
            color: txtColor,
          ),
          const SizedBox(height: 4),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              Prefs().textSelectionMode == TextSelectionMode.segmentation
                  ? L10n.of(context).reading_page_segmentation_mode
                  : L10n.of(context).reading_page_free_selection_mode,
              style: TextStyle(
                color: txtColor,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the text selection mode switch
  Widget _buildTextSelectionSwitch(Color txtColor) {
    return SwitchListTile(
      title: Text(
        Prefs().textSelectionMode == TextSelectionMode.segmentation
            ? L10n.of(context).reading_page_switch_to_free_selection
            : L10n.of(context).reading_page_switch_to_segmentation,
        style: TextStyle(color: txtColor, fontSize: 14),
      ),
      value: Prefs().textSelectionMode == TextSelectionMode.segmentation,
      activeColor: Theme.of(context).colorScheme.primary,
      onChanged: (value) {
        final newMode =
            value ? TextSelectionMode.segmentation : TextSelectionMode.free;

        // Update preference
        Prefs().textSelectionMode = newMode;

        // Notify EpubPlayer about the mode change
        widget.epubPlayerKey.currentState?.setTextSelectionMode(newMode);

        // Trigger rebuild to update UI
        setStateDebounced(() {});
      },
      contentPadding: EdgeInsets.zero,
    );
  }
}
